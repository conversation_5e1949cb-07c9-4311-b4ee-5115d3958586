{"nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 6,12,18 * * *"}]}}, "id": "fc4c68b1-5c22-4dc2-90ca-4c894362fdd2", "name": "Cost-Optimized Scheduler", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-2160, 140]}, {"parameters": {}, "id": "f80aa198-30b7-4153-bb91-655cce1f448a", "name": "Manual Test Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2160, 240]}, {"parameters": {"jsCode": "// ULTIMATE GOD Digital Marketing Configuration with AI Intelligence\nconst currentTime = new Date();\nconst currentDay = currentTime.getDay();\nconst currentHour = currentTime.getHours();\nconst rotationDay = currentDay === 0 ? 7 : currentDay;\n\n// Advanced AI-Powered Configuration\nconst ultimateConfig = {\n  // Company Branding & Identity\n  company: {\n    name: 'GOD Digital Marketing',\n    website: 'https://godigitalmarketing.com',\n    tagline: 'Transforming Businesses Through AI-Powered Digital Solutions',\n    value_proposition: 'We Generate 500%+ ROI Through AI-Powered Digital Marketing, Automation & Development Solutions',\n    mission: 'To democratize advanced digital marketing through AI automation',\n    vision: 'Becoming the global leader in AI-powered marketing transformation'\n  },\n  \n  // Comprehensive Service Ecosystem\n  services: {\n    core_digital_marketing: [\n      'Advanced SEO & Technical Optimization',\n      'AI-Powered PPC Campaign Management',\n      'Social Media Marketing & Community Building',\n      'Content Marketing & Strategic Storytelling',\n      'Email Marketing Automation & Personalization',\n      'Influencer Marketing & Partnership Development',\n      'Conversion Rate Optimization & A/B Testing',\n      'Advanced Marketing Analytics & Attribution',\n      'Brand Strategy & Digital Positioning',\n      'Online Reputation Management & Crisis Response'\n    ],\n    ai_automation_solutions: [\n      'ChatGPT & AI Chatbot Integration',\n      'Predictive Analytics & Machine Learning',\n      'Customer Journey Automation',\n      'AI-Powered Content Generation',\n      'Sentiment Analysis & Social Listening',\n      'Dynamic Personalization Engines',\n      'Automated Lead Scoring & Nurturing',\n      'AI-Driven Market Research',\n      'Intelligent Customer Segmentation',\n      'Automated Competitive Intelligence'\n    ],\n    business_automation: [\n      'Advanced n8n Workflow Development',\n      'CRM Integration & Automation',\n      'Sales Pipeline Optimization',\n      'Operations Process Automation',\n      'Document Management & Workflow',\n      'Inventory & Supply Chain Automation',\n      'Financial Process Automation',\n      'HR & Recruitment Automation',\n      'Quality Assurance Automation',\n      'Business Intelligence Dashboards'\n    ],\n    development_services: [\n      'Custom Web Application Development',\n      'E-commerce Platform Development',\n      'Progressive Web Apps (PWA)',\n      'Mobile App Development (iOS/Android)',\n      'API Development & Integration',\n      'Database Design & Optimization',\n      'Cloud Infrastructure Setup',\n      'DevOps & CI/CD Implementation',\n      'Security & Compliance Solutions',\n      'Performance Optimization & Monitoring'\n    ]\n  },\n  \n  // Advanced 7-Day Strategic Content Framework\n  content_strategy: {\n    rotation_cycle: 7,\n    current_day: rotationDay,\n    current_hour: currentHour,\n    strategies: {\n      1: { // Monday - Educational Excellence\n        type: 'educational',\n        focus: 'Educational Excellence & Authority Building',\n        goal: 'Establish thought leadership and provide transformational value',\n        psychology: 'Authority, Trust & Expertise',\n        content_pillars: [\n          'Advanced Marketing Strategies',\n          'AI Implementation Guides',\n          'Industry Deep Dives',\n          'Technical Tutorials',\n          'Future Predictions',\n          'Best Practice Frameworks'\n        ],\n        engagement_tactics: [\n          'Interactive tutorials',\n          'Live Q&A sessions',\n          'Expert interviews',\n          'Case study breakdowns',\n          'Tool demonstrations'\n        ],\n        cta_strategies: [\n          'Save this comprehensive guide',\n          'Share with your marketing team',\n          'Download the complete framework',\n          'Join our advanced masterclass',\n          'Get personalized implementation plan'\n        ],\n        hashtags: '#DigitalMarketing #MarketingStrategy #AIMarketing #BusinessGrowth #MarketingEducation #DigitalTransformation #MarketingTips #GODDigitalMarketing',\n        optimal_times: ['08:00', '12:00', '17:00'],\n        content_formats: ['carousel', 'video', 'infographic', 'thread', 'guide']\n      },\n      2: { // Tuesday - Achievement Showcase\n        type: 'achievements',\n        focus: 'Social Proof & Results Demonstration',\n        goal: 'Build credibility through documented success stories',\n        psychology: 'Social Proof, FOMO & Aspiration',\n        content_pillars: [\n          'Client Transformation Stories',\n          'ROI Case Studies',\n          'Before/After Comparisons',\n          'Testimonial Highlights',\n          'Award Recognition',\n          'Milestone Celebrations'\n        ],\n        engagement_tactics: [\n          'Video testimonials',\n          'Data visualizations',\n          'Client spotlights',\n          'Success metrics',\n          'Transformation timelines'\n        ],\n        cta_strategies: [\n          'Ready for similar results?',\n          'Book your transformation call',\n          'See how we can help you',\n          'Get your custom strategy',\n          'Join our success stories'\n        ],\n        hashtags: '#ClientSuccess #MarketingResults #BusinessTransformation #ROI #CaseStudy #MarketingWins #BusinessGrowth #GODDigitalMarketing',\n        optimal_times: ['09:00', '13:00', '18:00'],\n        content_formats: ['video', 'carousel', 'infographic', 'story', 'testimonial']\n      },\n      3: { // Wednesday - Strategic Marketing Psychology\n        type: 'marketing_psychology',\n        focus: 'Psychological Triggers & Urgency Creation',\n        goal: 'Create urgency and drive immediate action',\n        psychology: 'Scarcity, Urgency, Loss Aversion & FOMO',\n        content_pillars: [\n          'Consumer Psychology Insights',\n          'Conversion Optimization Secrets',\n          'Behavioral Marketing Tactics',\n          'Persuasion Techniques',\n          'Market Opportunity Alerts',\n          'Competitive Intelligence'\n        ],\n        engagement_tactics: [\n          'Psychological experiments',\n          'Behavior analysis',\n          'Conversion case studies',\n          'Market insights',\n          'Trend predictions'\n        ],\n        cta_strategies: [\n          'Don\\'t let competitors get ahead',\n          'Limited spots available',\n          'Act before it\\'s too late',\n          'Exclusive opportunity ends soon',\n          'Get ahead of the curve'\n        ],\n        hashtags: '#MarketingPsychology #ConversionOptimization #ConsumerBehavior #MarketingStrategy #BusinessPsychology #PersuasionMarketing #GODDigitalMarketing',\n        optimal_times: ['10:00', '14:00', '19:00'],\n        content_formats: ['video', 'carousel', 'poll', 'story', 'thread']\n      },\n      4: { // Thursday - Industry Trends & Innovation\n        type: 'trends_innovation',\n        focus: 'Thought Leadership & Industry Innovation',\n        goal: 'Position as industry visionary and innovator',\n        psychology: 'Authority, Innovation & Insider Knowledge',\n        content_pillars: [\n          'Emerging Technology Trends',\n          'Algorithm Updates Analysis',\n          'Market Disruption Insights',\n          'Future Predictions',\n          'Innovation Spotlights',\n          'Industry Research'\n        ],\n        engagement_tactics: [\n          'Trend analysis',\n          'Expert predictions',\n          'Technology reviews',\n          'Market research',\n          'Innovation showcases'\n        ],\n        cta_strategies: [\n          'Stay ahead with our insights',\n          'Get the competitive advantage',\n          'Join the innovation leaders',\n          'Access exclusive research',\n          'Be first to know'\n        ],\n        hashtags: '#MarketingTrends #DigitalInnovation #FutureOfMarketing #TechTrends #MarketingInnovation #IndustryInsights #DigitalTransformation #GODDigitalMarketing',\n        optimal_times: ['08:30', '13:30', '17:30'],\n        content_formats: ['video', 'infographic', 'thread', 'carousel', 'live']\n      },\n      5: { // Friday - Value-Driven Resources\n        type: 'free_resources',\n        focus: 'Value Delivery & Lead Generation',\n        goal: 'Generate qualified leads through valuable resources',\n        psychology: 'Reciprocity, Value & Community',\n        content_pillars: [\n          'Free Marketing Tools',\n          'Template Libraries',\n          'Automation Workflows',\n          'Strategy Frameworks',\n          'Educational Resources',\n          'Community Benefits'\n        ],\n        engagement_tactics: [\n          'Resource showcases',\n          'Tool demonstrations',\n          'Template previews',\n          'Workflow walkthroughs',\n          'Community highlights'\n        ],\n        cta_strategies: [\n          'Download for free instantly',\n          'Get lifetime access',\n          'Join our resource library',\n          'Claim your free toolkit',\n          'Access exclusive content'\n        ],\n        hashtags: '#FreeResources #MarketingTools #Templates #MarketingFreebies #ValueFirst #CommunityFirst #MarketingTemplates #GODDigitalMarketing',\n        optimal_times: ['11:00', '15:00', '20:00'],\n        content_formats: ['carousel', 'video', 'story', 'download', 'preview']\n      },\n      6: { // Saturday - Community & Engagement\n        type: 'community',\n        focus: 'Community Building & Relationship Development',\n        goal: 'Foster deep relationships and gather insights',\n        psychology: 'Belonging, Community & Social Connection',\n        content_pillars: [\n          'Community Spotlights',\n          'Interactive Discussions',\n          'Behind-the-Scenes Content',\n          'Team Introductions',\n          'User-Generated Content',\n          'Collaborative Projects'\n        ],\n        engagement_tactics: [\n          'Community polls',\n          'Q&A sessions',\n          'Member spotlights',\n          'Collaborative content',\n          'Interactive challenges'\n        ],\n        cta_strategies: [\n          'Join the conversation',\n          'Share your experience',\n          'Connect with peers',\n          'Be part of our community',\n          'Let\\'s discuss together'\n        ],\n        hashtags: '#MarketingCommunity #CommunityFirst #NetworkingTips #BusinessNetworking #MarketingSupport #CommunityBuilding #GODDigitalMarketing',\n        optimal_times: ['10:00', '16:00', '21:00'],\n        content_formats: ['poll', 'story', 'live', 'ugc', 'discussion']\n      },\n      7: { // Sunday - Inspiration & Motivation\n        type: 'motivational',\n        focus: 'Inspiration & Emotional Connection',\n        goal: 'Inspire action and create emotional bonds',\n        psychology: 'Inspiration, Motivation & Emotional Triggers',\n        content_pillars: [\n          'Success Stories',\n          'Motivational Insights',\n          'Journey Narratives',\n          'Transformation Stories',\n          'Inspirational Quotes',\n          'Vision Casting'\n        ],\n        engagement_tactics: [\n          'Story telling',\n          'Motivational videos',\n          'Inspirational quotes',\n          'Journey sharing',\n          'Vision casting'\n        ],\n        cta_strategies: [\n          'Start your journey today',\n          'Take the first step',\n          'Transform your business',\n          'Believe in your potential',\n          'Make it happen'\n        ],\n        hashtags: '#BusinessInspiration #EntrepreneurLife #SuccessStories #MarketingMotivation #BusinessTransformation #EntrepreneurJourney #GODDigitalMarketing',\n        optimal_times: ['09:00', '15:00', '19:00'],\n        content_formats: ['video', 'story', 'quote', 'carousel', 'testimonial']\n      }\n    }\n  },\n  \n  // AI Intelligence Configuration\n  ai_config: {\n    primary_model: 'meta-llama/llama-3.1-70b-versatile',\n    backup_models: ['gpt-4', 'claude-3-sonnet', 'gemini-pro'],\n    temperature: 0.7,\n    max_tokens: 4000,\n    creativity_level: 'high',\n    brand_consistency: 'strict',\n    content_quality_threshold: 8.5\n  },\n  \n  // Advanced Analytics Configuration\n  analytics: {\n    tracking_enabled: true,\n    real_time_monitoring: true,\n    performance_optimization: true,\n    predictive_analytics: true,\n    roi_attribution: true,\n    audience_insights: true\n  },\n  \n  // Platform-Specific Optimization\n  platform_config: {\n    facebook: {\n      optimal_length: '100-300 chars',\n      best_times: ['09:00', '13:00', '15:00'],\n      content_types: ['video', 'carousel', 'link'],\n      engagement_focus: 'community_building'\n    },\n    instagram: {\n      optimal_length: '125-150 chars',\n      best_times: ['11:00', '14:00', '17:00', '20:00'],\n      content_types: ['photo', 'carousel', 'reel', 'story'],\n      engagement_focus: 'visual_storytelling'\n    },\n    linkedin: {\n      optimal_length: '150-300 chars',\n      best_times: ['08:00', '12:00', '17:00'],\n      content_types: ['article', 'post', 'video'],\n      engagement_focus: 'professional_networking'\n    },\n    twitter: {\n      optimal_length: '71-100 chars',\n      best_times: ['09:00', '13:00', '16:00', '19:00'],\n      content_types: ['tweet', 'thread', 'poll'],\n      engagement_focus: 'real_time_engagement'\n    },\n    youtube: {\n      optimal_length: '200+ chars',\n      best_times: ['18:00', '19:00', '20:00'],\n      content_types: ['video', 'short', 'live'],\n      engagement_focus: 'educational_content'\n    },\n    tiktok: {\n      optimal_length: '100-150 chars',\n      best_times: ['18:00', '19:00', '21:00'],\n      content_types: ['video', 'trend'],\n      engagement_focus: 'viral_content'\n    },\n    pinterest: {\n      optimal_length: '200+ chars',\n      best_times: ['20:00', '21:00', '22:00'],\n      content_types: ['pin', 'idea'],\n      engagement_focus: 'search_optimization'\n    }\n  },\n  \n  // Current Strategy Selection\n  todays_strategy: null,\n  optimal_posting_time: null\n};\n\n// Intelligent Strategy Selection\nultimateConfig.todays_strategy = ultimateConfig.content_strategy.strategies[rotationDay];\n\n// AI-Powered Optimal Timing\nconst strategy = ultimateConfig.todays_strategy;\nconst optimalTimes = strategy.optimal_times;\nconst nextOptimalTime = optimalTimes.find(time => {\n  const [hour, minute] = time.split(':').map(Number);\n  return hour > currentHour || (hour === currentHour && minute > currentTime.getMinutes());\n}) || optimalTimes[0];\n\nultimateConfig.optimal_posting_time = nextOptimalTime;\n\nreturn {\n  ...ultimateConfig,\n  rotation_day: rotationDay,\n  current_hour: currentHour,\n  day_name: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDay],\n  next_optimal_time: nextOptimalTime,\n  config_version: 'ultimate_v2.0',\n  timestamp: currentTime.toISOString()\n};"}, "id": "73b5fa3d-0b16-4374-9d8b-4da0da3883ec", "name": "Ultimate AI Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1960, 180]}, {"parameters": {"jsCode": "// AI-Powered Audience Intelligence & Optimal Timing\nconst config = $input.first().json;\nconst currentTime = new Date();\nconst strategy = config.todays_strategy;\n\n// Advanced Audience Analytics\nconst audienceIntelligence = {\n  peak_engagement_hours: {\n    monday: ['08:00', '12:00', '17:00'],\n    tuesday: ['09:00', '13:00', '18:00'],\n    wednesday: ['10:00', '14:00', '19:00'],\n    thursday: ['08:30', '13:30', '17:30'],\n    friday: ['11:00', '15:00', '20:00'],\n    saturday: ['10:00', '16:00', '21:00'],\n    sunday: ['09:00', '15:00', '19:00']\n  },\n  audience_behavior: {\n    educational: { engagement_rate: 0.85, best_format: 'carousel', optimal_length: 300 },\n    achievements: { engagement_rate: 0.92, best_format: 'video', optimal_length: 250 },\n    marketing_psychology: { engagement_rate: 0.88, best_format: 'thread', optimal_length: 280 },\n    trends_innovation: { engagement_rate: 0.90, best_format: 'infographic', optimal_length: 320 },\n    free_resources: { engagement_rate: 0.95, best_format: 'carousel', optimal_length: 200 },\n    community: { engagement_rate: 0.87, best_format: 'poll', optimal_length: 150 },\n    motivational: { engagement_rate: 0.89, best_format: 'video', optimal_length: 180 }\n  },\n  platform_preferences: {\n    linkedin: { peak_days: ['tuesday', 'wednesday', 'thursday'], content_preference: 'professional' },\n    instagram: { peak_days: ['friday', 'saturday', 'sunday'], content_preference: 'visual' },\n    twitter: { peak_days: ['monday', 'tuesday', 'wednesday'], content_preference: 'realtime' },\n    facebook: { peak_days: ['thursday', 'friday', 'saturday'], content_preference: 'community' },\n    youtube: { peak_days: ['friday', 'saturday', 'sunday'], content_preference: 'educational' },\n    tiktok: { peak_days: ['friday', 'saturday', 'sunday'], content_preference: 'entertaining' },\n    pinterest: { peak_days: ['saturday', 'sunday', 'monday'], content_preference: 'inspirational' }\n  }\n};\n\n// Intelligent Content Optimization\nconst contentOptimization = {\n  current_strategy: strategy,\n  audience_behavior: audienceIntelligence.audience_behavior[strategy.type],\n  optimal_timing: audienceIntelligence.peak_engagement_hours[config.day_name.toLowerCase()],\n  platform_focus: Object.entries(audienceIntelligence.platform_preferences)\n    .filter(([platform, prefs]) => prefs.peak_days.includes(config.day_name.toLowerCase()))\n    .map(([platform]) => platform),\n  content_recommendations: {\n    primary_format: audienceIntelligence.audience_behavior[strategy.type].best_format,\n    optimal_length: audienceIntelligence.audience_behavior[strategy.type].optimal_length,\n    expected_engagement: audienceIntelligence.audience_behavior[strategy.type].engagement_rate,\n    priority_platforms: Object.entries(audienceIntelligence.platform_preferences)\n      .filter(([platform, prefs]) => prefs.peak_days.includes(config.day_name.toLowerCase()))\n      .map(([platform]) => platform)\n  }\n};\n\n// AI Decision Engine\nconst aiDecisions = {\n  should_post_now: config.optimal_posting_time === currentTime.getHours() + ':' + String(currentTime.getMinutes()).padStart(2, '0'),\n  content_urgency: strategy.type === 'marketing_psychology' ? 'high' : 'medium',\n  engagement_prediction: contentOptimization.audience_behavior.engagement_rate * 100,\n  recommended_action: contentOptimization.content_recommendations.expected_engagement > 0.9 ? 'proceed' : 'optimize',\n  quality_threshold: config.ai_config.content_quality_threshold\n};\n\nreturn {\n  ...contentOptimization,\n  ai_decisions: aiDecisions,\n  intelligence_ready: true,\n  optimization_score: Math.round(contentOptimization.audience_behavior.engagement_rate * 10),\n  timestamp: currentTime.toISOString()\n};"}, "id": "fcfea1d1-b2d3-47b3-b385-b06c58071039", "name": "AI Audience Intelligence", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1760, 180]}, {"parameters": {"url": "https://www.reddit.com/r/all/hot.json?limit=10", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "n8n-workflow"}, {"name": "Accept", "value": "application/json"}]}, "options": {}}, "id": "9a22f9e4-0b79-4ec7-905a-af47f86ddd73", "name": "Multi-Source Trend Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1560, 160]}, {"parameters": {"url": "https://feeds.feedburner.com/TechCrunch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "n8n-workflow-bot/1.0"}, {"name": "Accept", "value": "application/rss+xml, application/xml, text/xml"}]}, "options": {}}, "id": "334036b8-ce22-4070-aad9-a7161dd4a107", "name": "Industry News Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1560, 380]}, {"parameters": {"jsCode": "// INDEPENDENT TREND ANALYZER - No failed node dependencies\n\n// Get only the working inputs\nconst audienceIntel = $('AI Audience Intelligence').item.json;\nconst config = $('Ultimate AI Configuration').item.json;\nconst strategy = config.todays_strategy;\n\n// Try to get Reddit data, but don't fail if it's not available\nlet redditData = [];\nlet redditTrends = [];\nlet hotTopics = [];\n\ntry {\n  const redditResult = $('Multi-Source Trend Research').item?.json;\n  if (redditResult && redditResult.length > 0 && redditResult[0].data && redditResult[0].data.children) {\n    redditTrends = redditResult[0].data.children\n      .filter(post => post.data && post.data.title && post.data.ups > 1000)\n      .slice(0, 8)\n      .map(post => ({\n        title: post.data.title,\n        subreddit: post.data.subreddit,\n        upvotes: post.data.ups,\n        source: 'reddit'\n      }));\n    \n    hotTopics = redditTrends.map(trend => trend.title);\n  }\n} catch (error) {\n  console.log('Reddit data not available, using fallback');\n}\n\n// Enhanced fallback content by strategy type\nconst strategyContent = {\n  'educational': {\n    trends: [\n      'AI Marketing Automation 2025',\n      'Advanced SEO Techniques',\n      'Content Marketing Strategy',\n      'Digital Transformation Guide',\n      'Social Media Optimization',\n      'Email Marketing Best Practices'\n    ],\n    keywords: ['tutorial', 'guide', 'how to', 'best practices', 'strategy', 'framework']\n  },\n  'achievements': {\n    trends: [\n      'Client Success Stories',\n      '500% ROI Improvements',\n      'Business Transformation Results',\n      'Marketing Case Studies',\n      'Growth Metrics That Matter',\n      'Conversion Rate Optimization Wins'\n    ],\n    keywords: ['success', 'results', 'growth', 'transformation', 'ROI', 'case study']\n  },\n  'marketing_psychology': {\n    trends: [\n      'Consumer Behavior Insights 2025',\n      'Conversion Psychology Secrets',\n      'Persuasion Techniques That Work',\n      'Customer Psychology Triggers',\n      'Behavioral Marketing Tactics',\n      'Neuromarketing Strategies'\n    ],\n    keywords: ['psychology', 'behavior', 'persuasion', 'influence', 'conversion', 'triggers']\n  },\n  'trends_innovation': {\n    trends: [\n      'AI Marketing Trends 2025',\n      'Future of Digital Marketing',\n      'Marketing Innovation Breakthrough',\n      'Technology Disruption in Marketing',\n      'ChatGPT for Business Growth',\n      'Automation Revolution'\n    ],\n    keywords: ['trends', 'innovation', 'future', 'AI', 'technology', 'disruption']\n  },\n  'free_resources': {\n    trends: [\n      'Free Marketing Tools Collection',\n      'Templates Library 2025',\n      'Resource Toolkit Download',\n      'Marketing Frameworks Guide',\n      'Free Strategy Templates',\n      'Complete Automation Guides'\n    ],\n    keywords: ['free', 'template', 'tool', 'resource', 'download', 'toolkit']\n  },\n  'community': {\n    trends: [\n      'Marketing Community Growth',\n      'Professional Networking Tips',\n      'Industry Events 2025',\n      'Knowledge Sharing Platforms',\n      'Collaboration Success Stories',\n      'Marketing Mastermind Groups'\n    ],\n    keywords: ['community', 'network', 'collaborate', 'share', 'connect', 'mastermind']\n  },\n  'motivational': {\n    trends: [\n      'Entrepreneur Success Stories',\n      'Business Inspiration Daily',\n      'Growth Mindset Strategies',\n      'Achievement Goal Setting',\n      'Success Transformation Journey',\n      'Motivation for Business Owners'\n    ],\n    keywords: ['inspiration', 'motivation', 'success', 'growth', 'achievement', 'transformation']\n  }\n};\n\n// Get content for current strategy\nconst currentStrategy = strategyContent[strategy.type] || strategyContent.educational;\n\n// Combine trending topics (Reddit + strategy-specific)\nconst allTrends = [\n  ...hotTopics,           // Live Reddit trends (if available)\n  ...currentStrategy.trends    // Strategy-specific trends\n];\n\n// Create final processed data\nconst selectedTrends = allTrends.slice(0, 12);\nconst primaryKeywords = currentStrategy.keywords;\n\n// Extract keywords from Reddit trends if available\nconst trendingKeywords = hotTopics.length > 0 \n  ? hotTopics\n      .flatMap(topic => topic.toLowerCase().split(' '))\n      .filter(word => word.length > 3)\n      .slice(0, 6)\n  : ['marketing', 'business', 'growth', 'strategy', 'automation', 'success'];\n\n// Content enhancement suggestions\nconst contentEnhancements = {\n  trending_topics: selectedTrends.slice(0, 5),\n  relevant_keywords: [...primaryKeywords.slice(0, 5), ...trendingKeywords.slice(0, 5)],\n  content_angles: [\n    `Latest ${strategy.type} trends transforming businesses in 2025`,\n    `How to leverage ${strategy.type} for massive business growth`,\n    `Why ${strategy.type} is the key to competitive advantage`,\n    `Future-proof your business with advanced ${strategy.type} strategies`,\n    `The ultimate ${strategy.type} guide for modern businesses`\n  ],\n  hashtag_suggestions: [\n    '#DigitalMarketing',\n    '#MarketingTrends',\n    '#BusinessGrowth',\n    '#MarketingStrategy',\n    '#Innovation',\n    '#Automation',\n    '#Success',\n    '#GODDigitalMarketing'\n  ],\n  viral_hooks: [\n    `🚀 The ${strategy.type} strategy that's changing everything`,\n    `💡 Why 90% of businesses fail at ${strategy.type} (and how to be the 10%)`,\n    `🔥 The ${strategy.type} secret that generated $1M+ for our clients`,\n    `⚡ Revolutionary ${strategy.type} approach that's disrupting the industry`\n  ]\n};\n\n// Calculate optimization metrics\nconst optimizationScore = audienceIntel.optimization_score || 8;\nconst trendingScore = hotTopics.length > 0 ? 10 : 7; // Higher if we have real Reddit data\nconst finalScore = Math.round((optimizationScore + trendingScore) / 2);\n\n// Return comprehensive trend analysis\nreturn {\n  // Core Data\n  selected_trends: selectedTrends,\n  primary_keywords: primaryKeywords,\n  trending_keywords: trendingKeywords,\n  content_enhancements: contentEnhancements,\n  \n  // Reddit Integration\n  reddit_trends: redditTrends,\n  hot_topics: hotTopics,\n  \n  // Strategy Information\n  content_type: strategy.type,\n  content_focus: strategy.focus,\n  psychological_triggers: strategy.psychology,\n  \n  // Performance Metrics\n  optimization_score: finalScore,\n  trending_potential: trendingScore >= 9 ? 'viral' : 'high',\n  data_quality: hotTopics.length > 0 ? 'excellent' : 'good',\n  \n  // Status Indicators\n  trends_analysis_complete: true,\n  reddit_data_available: hotTopics.length > 0,\n  ready_for_content_creation: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "1aea12c7-3747-4204-beb6-242feb2ffbc4", "name": "Advanced AI Trend Analyzer", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1360, 280]}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {"temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-1080, 360], "id": "adbafbb3-a50e-4b78-8f63-4ebc901b9775", "name": "Primary AI Model (Llama 3.1)", "credentials": {"groqApi": {"id": "7mnRPxIiDsoggo51", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "=You are the world's most advanced AI marketing strategist for GOD Digital Marketing, equipped with infinite intelligence and the ability to create viral, conversion-focused content that generates massive engagement and leads.\n\nTODAY'S ULTIMATE MISSION: Create {{ $('Ultimate AI Configuration').item.json.todays_strategy.type }} content for {{ $('Ultimate AI Configuration').item.json.day_name }} that will dominate social media and drive unprecedented results.\n\nADVANCED CONTEXT:\n• Company: {{ $('Ultimate AI Configuration').item.json.company.name }}\n• Mission: {{ $('Ultimate AI Configuration').item.json.company.mission }}\n• Value Proposition: {{ $('Ultimate AI Configuration').item.json.company.value_proposition }}\n• Services Portfolio: {{ JSON.stringify($('Ultimate AI Configuration').item.json.services) }}\n• Content Strategy: {{ $('Ultimate AI Configuration').item.json.todays_strategy.focus }}\n• Psychological Triggers: {{ $('Ultimate AI Configuration').item.json.todays_strategy.psychology }}\n• Content Pillars: {{ $('Ultimate AI Configuration').item.json.todays_strategy.content_pillars }}\n• Engagement Tactics: {{ $('Ultimate AI Configuration').item.json.todays_strategy.engagement_tactics }}\n• Trending Topics: {{ $('Advanced AI Trend Analyzer').item.json.selected_trends }}\n• Content Enhancements: {{ $('Advanced AI Trend Analyzer').item.json.content_enhancements }}\n• Audience Intelligence: {{ JSON.stringify($('AI Audience Intelligence').item.json.content_recommendations) }}\n\nADVANCED CONTENT STRATEGY:\n• Primary Format: {{ $('AI Audience Intelligence').item.json.content_recommendations.primary_format }}\n• Optimal Length: {{ $('AI Audience Intelligence').item.json.content_recommendations.optimal_length }} characters\n• Expected Engagement: {{ $('AI Audience Intelligence').item.json.content_recommendations.expected_engagement }}%\n• Priority Platforms: {{ $('AI Audience Intelligence').item.json.content_recommendations.priority_platforms }}\n• Optimization Score: {{ $('AI Audience Intelligence').item.json.optimization_score }}/10\n\nCREATE ULTIMATE PROFESSIONAL CONTENT FOR ALL PLATFORMS:\n1. FACEBOOK_POST: Community-focused, story-driven, highly engaging (include https://godigitalmarketing.com)\n2. FACEBOOK_CAROUSEL: Multi-slide educational content with strong CTAs\n3. INSTAGRAM_CAPTION: Visual storytelling, hashtag-optimized, action-oriented\n4. INSTAGRAM_STORY: Quick, swipeable, compelling CTA with interactive elements\n5. INSTAGRAM_REEL_SCRIPT: Trending, educational, hook-heavy for maximum virality\n6. LINKEDIN_POST: Professional, thought leadership, B2B focused with industry insights\n7. LINKEDIN_ARTICLE_OUTLINE: Comprehensive article structure with key points\n8. TWITTER_THREAD: Educational, viral potential, 10-15 tweets with strong narrative\n9. TWITTER_SINGLE: Punchy, retweetable, trending with maximum impact\n10. YOUTUBE_TITLE: SEO-optimized, click-worthy, under 60 characters\n11. YOUTUBE_DESCRIPTION: Detailed, timestamp-rich, link-optimized with strong CTAs\n12. YOUTUBE_SCRIPT_OUTLINE: Video script structure with hooks, content, and CTAs\n13. TIKTOK_SCRIPT: Trending, educational, hook-heavy with viral potential\n14. PINTEREST_TITLE: SEO-focused, keyword-rich for maximum discoverability\n15. PINTEREST_DESCRIPTION: Search-optimized, actionable with strong value proposition\n16. REDDIT_TITLE: Community-friendly, discussion-starter, authentic\n17. REDDIT_POST: Value-first, authentic, helpful with natural engagement\n18. DISCORD_MESSAGE: Community-focused, engaging with rich formatting\n19. TELEGRAM_MESSAGE: Direct, actionable, link-optimized for immediate response\n20. EMAIL_SUBJECT: High open-rate subject line with curiosity and urgency\n21. EMAIL_PREVIEW: Compelling preview text that drives opens\n\nULTIMATE REQUIREMENTS:\n• Include https://godigitalmarketing.com strategically in each post\n• Use specific numbers, results, and case studies when relevant to {{ $('Ultimate AI Configuration').item.json.todays_strategy.type }}\n• Avoid AI-sounding phrases - write naturally and conversationally\n• Make each post conversion-focused with psychological triggers from {{ $('Ultimate AI Configuration').item.json.todays_strategy.psychology }}\n• Include trending topics from {{ $('Advanced AI Trend Analyzer').item.json.selected_trends }}\n• Incorporate relevant hashtags optimized for each platform\n• Maintain GOD Digital Marketing's authoritative yet approachable voice\n• Each post should drive action and build community\n• Ensure content matches the {{ $('Ultimate AI Configuration').item.json.todays_strategy.type }} theme perfectly\n• Use engagement tactics: {{ $('Ultimate AI Configuration').item.json.todays_strategy.engagement_tactics }}\n• Include CTA strategies: {{ $('Ultimate AI Configuration').item.json.todays_strategy.cta_strategies }}\n• Optimize for {{ $('AI Audience Intelligence').item.json.content_recommendations.primary_format }} format\n• Target {{ $('AI Audience Intelligence').item.json.content_recommendations.optimal_length }} character length where appropriate\n\nFormat as JSON with all platform keys. Make it irresistible, results-driven, and absolutely viral."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-1060, 20], "id": "e671c73d-bb68-447f-8315-66ad77c4a2bf", "name": "Ultimate Content Creator AI"}, {"parameters": {"jsCode": "// Ultimate Content Processor with AI Intelligence & Quality Assurance\nconst aiResponse = $input.first()?.json?.response || $input.first()?.json?.output || '';\nconst config = $('Ultimate AI Configuration').item.json;\nconst strategy = config.todays_strategy;\nconst audienceIntel = $('AI Audience Intelligence').item.json;\nconst trendAnalysis = $('Advanced AI Trend Analyzer').item.json;\n\nlet socialContent = {};\n\n// Advanced AI Response Processing\ntry {\n  // Try multiple JSON extraction methods\n  let jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n  if (jsonMatch) {\n    socialContent = JSON.parse(jsonMatch[0]);\n  } else {\n    // Try alternative extraction\n    const lines = aiResponse.split('\\n');\n    const jsonLines = lines.filter(line => line.trim().startsWith('\"') || line.trim().startsWith('{') || line.trim().startsWith('}'));\n    if (jsonLines.length > 0) {\n      socialContent = JSON.parse(jsonLines.join('\\n'));\n    }\n  }\n} catch (error) {\n  console.log('AI content parsing failed, using premium fallback system');\n}\n\n// Ultimate Premium Fallback Content Generation\nif (Object.keys(socialContent).length === 0) {\n  const hooks = strategy.content_pillars.map(pillar => \n    `🚀 ${pillar}: The game-changing strategy that's transforming businesses`\n  );\n  const selectedHook = hooks[Math.floor(Math.random() * hooks.length)];\n  \n  const angles = trendAnalysis.content_enhancements.content_angles;\n  const selectedAngle = angles[Math.floor(Math.random() * angles.length)];\n  \n  const leadMagnets = [\n    `Free ${strategy.type} Masterclass + Complete Implementation Guide`,\n    `Advanced ${strategy.type} Toolkit (Worth $5,000) - Limited Time`,\n    `Exclusive ${strategy.type} Strategy Session + Custom Action Plan`,\n    `Complete ${strategy.type} Resource Library + VIP Community Access`\n  ];\n  const selectedMagnet = leadMagnets[Math.floor(Math.random() * leadMagnets.length)];\n  \n  const ctas = strategy.cta_strategies;\n  const selectedCTA = ctas[Math.floor(Math.random() * ctas.length)];\n  \n  const hashtags = trendAnalysis.content_enhancements.hashtag_suggestions.join(' ');\n  \n  // Generate ultimate content for all platforms\n  socialContent = {\n    facebook_post: `${selectedHook}\\n\\n${selectedAngle}\\n\\n🎁 EXCLUSIVE: ${selectedMagnet}\\n\\n${selectedCTA}\\n\\nWhat's your experience with this? Share your thoughts below! 👇\\n\\n🔗 Transform your business: https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    facebook_carousel: `Slide 1: ${selectedHook}\\nSlide 2: ${selectedAngle}\\nSlide 3: Key Benefits & Results\\nSlide 4: ${selectedMagnet}\\nSlide 5: ${selectedCTA} - https://godigitalmarketing.com`,\n    \n    instagram_caption: `${selectedHook} ✨\\n\\n${selectedAngle}\\n\\n🔥 ${selectedMagnet}\\n\\n${selectedCTA}\\n\\n💭 Save this post and share with someone who needs this!\\n\\n🔗 Link in bio: https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    instagram_story: `${selectedHook}\\n\\n${selectedAngle.substring(0, 120)}...\\n\\n🔥 ${selectedMagnet}\\n\\nSwipe up to learn more! 👆`,\n    \n    instagram_reel_script: `Hook: ${selectedHook}\\nProblem: ${selectedAngle.split('.')[0]}\\nSolution: ${selectedAngle.split('.').slice(1).join('.')}\\nProof: Real client results\\nCTA: ${selectedCTA}\\nLink: https://godigitalmarketing.com`,\n    \n    linkedin_post: `${selectedHook}\\n\\n${selectedAngle}\\n\\n🎯 Key Insight: This strategy has transformed 500+ businesses in our portfolio, generating an average ROI of 500%+.\\n\\n💡 ${selectedMagnet}\\n\\n${selectedCTA}\\n\\nWhat's been your experience with this approach? Share your insights below.\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags.replace(/#/g, '')}`,\n    \n    linkedin_article_outline: `Title: ${selectedHook}\\n\\nIntroduction: ${selectedAngle}\\n\\nSection 1: Current Challenges\\nSection 2: Our Solution\\nSection 3: Case Studies & Results\\nSection 4: Implementation Strategy\\nSection 5: ${selectedMagnet}\\n\\nConclusion: ${selectedCTA}\\n\\nCTA: https://godigitalmarketing.com`,\n    \n    twitter_thread: `🧵 THREAD: ${selectedHook} (1/12)\\n\\n${selectedAngle}\\n\\nHere's what we've learned from transforming 500+ businesses... 👇\\n\\n2/12 ${selectedAngle.split('.')[0]}...\\n\\n12/12 ${selectedCTA}\\n\\n🔗 https://godigitalmarketing.com`,\n    \n    twitter_single: `${selectedHook}\\n\\n${selectedAngle.substring(0, 180)}...\\n\\n${selectedCTA}\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    youtube_title: `${selectedHook.replace(/🚀|💡|🎯|⚡|🔥|✨/, '')} | GOD Digital Marketing`,\n    \n    youtube_description: `${selectedAngle}\\n\\n🎁 ${selectedMagnet}\\n\\n${selectedCTA}\\n\\n⏰ TIMESTAMPS:\\n0:00 Introduction\\n1:30 The Challenge\\n3:00 Our Solution\\n5:30 Implementation\\n7:00 Results & Case Studies\\n8:30 Next Steps\\n10:00 ${selectedMagnet}\\n\\n🔗 USEFUL LINKS:\\n• Free Resources: https://godigitalmarketing.com\\n• Book a Strategy Call: https://godigitalmarketing.com/contact\\n• Follow Us: https://godigitalmarketing.com/social\\n\\n${hashtags}\\n\\n---\\nGOD Digital Marketing - Transforming Businesses Through AI-Powered Solutions\\n#GODDigitalMarketing #DigitalTransformation`,\n    \n    youtube_script_outline: `Hook: ${selectedHook}\\nProblem: Current challenges in ${strategy.type}\\nSolution: Our proven approach\\nProof: Client results and case studies\\nPlan: Implementation strategy\\nCTA: ${selectedCTA} - https://godigitalmarketing.com`,\n    \n    tiktok_script: `Hook: ${selectedHook}\\nProblem: ${selectedAngle.split('.')[0]}\\nSolution: ${selectedAngle.split('.').slice(1).join('.')}\\nProof: Real client results\\nCTA: ${selectedCTA}\\nLink: https://godigitalmarketing.com\\nHashtags: ${hashtags}`,\n    \n    pinterest_title: `${selectedAngle.split('.')[0]} | ${selectedMagnet} | GOD Digital Marketing`,\n    \n    pinterest_description: `${selectedAngle}\\n\\n✅ ${selectedMagnet}\\n\\n${selectedCTA}\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    reddit_title: selectedHook.replace(/🚀|💡|🎯|⚡|🔥|✨/, '').trim(),\n    \n    reddit_post: `${selectedAngle}\\n\\n**${selectedMagnet}**\\n\\n${selectedCTA}\\n\\nWhat's been your experience with this? Would love to hear your thoughts and answer any questions!\\n\\nMore resources: https://godigitalmarketing.com`,\n    \n    discord_message: `🚀 **${selectedHook}**\\n\\n${selectedAngle}\\n\\n💡 **${selectedMagnet}**\\n\\n${selectedCTA}\\n\\n🔗 https://godigitalmarketing.com\\n\\n@everyone What do you think about this strategy?`,\n    \n    telegram_message: `🎯 ${selectedHook}\\n\\n${selectedAngle}\\n\\n🎁 ${selectedMagnet}\\n\\n${selectedCTA}\\n\\n🔗 https://godigitalmarketing.com\\n\\nJoin our VIP community for exclusive content!`,\n    \n    email_subject: `${selectedHook.replace(/🚀|💡|🎯|⚡|🔥|✨/, '')} [Limited Time]`,\n    \n    email_preview: `${selectedAngle.substring(0, 90)}... Don't miss this opportunity!`\n  };\n}\n\n// Advanced Quality Assessment with AI Intelligence\nconst qualityMetrics = {\n  content_length_check: Object.values(socialContent).every(content => \n    content && content.length >= 50 && content.length <= 3000\n  ),\n  platform_coverage: Object.keys(socialContent).length >= 15,\n  cta_presence: Object.values(socialContent).every(content => \n    content && (\n      content.includes('DM') || content.includes('Comment') || content.includes('Click') || \n      content.includes('Visit') || content.includes('Book') || content.includes('Download') ||\n      content.includes('Join') || content.includes('Get') || content.includes('Access')\n    )\n  ),\n  website_link: Object.values(socialContent).every(content => \n    content && content.includes('godigitalmarketing.com')\n  ),\n  engagement_elements: Object.values(socialContent).every(content => \n    content && (\n      content.includes('?') || content.includes('👇') || content.includes('comment') || \n      content.includes('share') || content.includes('tag') || content.includes('save')\n    )\n  ),\n  brand_consistency: Object.values(socialContent).every(content => \n    content && (\n      content.includes('GOD') || content.includes('Digital') || content.includes('Marketing') ||\n      content.includes('transform') || content.includes('AI-powered')\n    )\n  ),\n  trending_integration: Object.values(socialContent).some(content => \n    trendAnalysis.selected_trends.some(trend => \n      content.toLowerCase().includes(trend.toLowerCase().split(' ')[0])\n    )\n  ),\n  psychological_triggers: Object.values(socialContent).every(content => \n    strategy.psychology.split(' & ').some(trigger => \n      content.toLowerCase().includes(trigger.toLowerCase().split(' ')[0])\n    )\n  )\n};\n\nconst qualityScore = Object.values(qualityMetrics).filter(Boolean).length / Object.keys(qualityMetrics).length * 10;\nconst qualityPass = qualityScore >= config.ai_config.content_quality_threshold;\n\n// AI Performance Prediction\nconst performancePrediction = {\n  expected_engagement_rate: audienceIntel.content_recommendations.expected_engagement,\n  viral_potential: qualityScore > 9 ? 'high' : qualityScore > 7 ? 'medium' : 'low',\n  conversion_likelihood: qualityPass ? 'high' : 'medium',\n  optimization_recommendations: qualityScore < 8 ? [\n    'Enhance psychological triggers',\n    'Improve trending topic integration',\n    'Strengthen call-to-action'\n  ] : ['Content optimized for maximum performance']\n};\n\nreturn {\n  ...socialContent,\n  quality_metrics: {\n    ...qualityMetrics,\n    overall_score: qualityScore,\n    quality_pass: qualityPass,\n    platforms_ready: Object.keys(socialContent).length,\n    ai_confidence: qualityScore >= 8 ? 'high' : 'medium'\n  },\n  performance_prediction: performancePrediction,\n  content_type: strategy.type,\n  content_focus: strategy.focus,\n  psychological_triggers: strategy.psychology,\n  trending_topics: trendAnalysis.selected_trends.slice(0, 5),\n  primary_keywords: trendAnalysis.primary_keywords,\n  optimization_score: audienceIntel.optimization_score,\n  processing_complete: true,\n  ai_enhanced: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "525b5c3d-1883-4483-8386-a745b6839ddf", "name": "Ultimate AI Content Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-760, 180]}, {"parameters": {"url": "https://api.unsplash.com/search/photos", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('Ultimate AI Content Processor').item.json.primary_keywords[0] || 'digital marketing' }}"}, {"name": "per_page", "value": "10"}, {"name": "orientation", "value": "landscape"}, {"name": "content_filter", "value": "high"}, {"name": "order_by", "value": "relevant"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Client-ID 7l4GL0n2dGxLBvqIb9rroC8RfYNQgTuHWyLqsdcQsjA"}, {"name": "Accept", "value": "application/json"}, {"name": "User-Agent", "value": "n8n-workflow"}]}, "options": {}}, "id": "0db5056f-ca40-41c7-abcc-1bcbbae287a5", "name": "Primary Image Search (Unsplash)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-560, 200]}, {"parameters": {"url": "https://api.pexels.com/v1/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('Ultimate AI Content Processor').item.json.primary_keywords[0] || 'digital marketing business' }}"}, {"name": "per_page", "value": "10"}, {"name": "orientation", "value": "landscape"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "563492ad6f91700001000001cdf2b1c2c8a04e3bb4f1f9b3d6c8e5c4"}, {"name": "Accept", "value": "application/json"}]}, "options": {}}, "id": "01c7ab83-14ba-498b-98ba-df5b0b91af1b", "name": "Backup Image Search (Pexels)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-560, 380]}, {"parameters": {"jsCode": "// Ultimate AI-Powered Image Processing & Optimization System - FIXED\n\n// Safely get all inputs with proper error handling\nlet unsplashData = {};\nlet pexelsData = {};\nlet contentData = {};\nlet config = {};\nlet strategy = {};\n\ntry {\n  unsplashData = $('Primary Image Search (Unsplash)').item?.json || {};\n} catch (error) {\n  console.log('Unsplash data not available');\n}\n\ntry {\n  pexelsData = $('Backup Image Search (Pexels)').item?.json || {};\n} catch (error) {\n  console.log('Pexels data not available');\n}\n\ntry {\n  contentData = $('Ultimate AI Content Processor').item.json;\n} catch (error) {\n  console.log('Content data not available');\n  contentData = { primary_keywords: ['digital marketing'] };\n}\n\ntry {\n  config = $('Ultimate AI Configuration').item.json;\n  strategy = config.todays_strategy || {};\n} catch (error) {\n  console.log('Config data not available');\n  strategy = { type: 'educational' };\n}\n\nlet selectedImages = [];\nlet primaryImage = null;\n\n// Process Unsplash data - handle both empty results and no data\nif (unsplashData.results && Array.isArray(unsplashData.results) && unsplashData.results.length > 0) {\n  selectedImages = unsplashData.results.slice(0, 5).map(img => ({\n    id: img.id,\n    url: img.urls.regular,\n    url_hd: img.urls.full,\n    url_thumb: img.urls.thumb,\n    alt: img.alt_description || `${contentData.primary_keywords[0]} - GOD Digital Marketing`,\n    credit: `Photo by ${img.user.name} on Unsplash`,\n    source: 'unsplash',\n    quality_score: 10,\n    width: img.width,\n    height: img.height\n  }));\n  primaryImage = selectedImages[0];\n}\n\n// Process Pexels data if Unsplash failed or returned no results\nif (!primaryImage && pexelsData.photos && Array.isArray(pexelsData.photos) && pexelsData.photos.length > 0) {\n  selectedImages = pexelsData.photos.slice(0, 5).map(img => ({\n    id: img.id,\n    url: img.src.large,\n    url_hd: img.src.original,\n    url_thumb: img.src.medium,\n    alt: `${contentData.primary_keywords[0]} - GOD Digital Marketing`,\n    credit: `Photo by ${img.photographer} on Pexels`,\n    source: 'pexels',\n    quality_score: 9,\n    width: img.width,\n    height: img.height\n  }));\n  primaryImage = selectedImages[0];\n}\n\n// FALLBACK: High-quality images when APIs return no results\nif (!primaryImage) {\n  const keyword = contentData.primary_keywords ? contentData.primary_keywords[0] : 'business';\n  const safeKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();\n  \n  // Multiple fallback options\n  const fallbackImages = [\n    {\n      id: 'unsplash_direct_1',\n      url: `https://source.unsplash.com/1200x630/?${safeKeyword},business,marketing`,\n      url_hd: `https://source.unsplash.com/1920x1080/?${safeKeyword},business,marketing`,\n      url_thumb: `https://source.unsplash.com/400x300/?${safeKeyword},business,marketing`,\n      alt: `${keyword} - Professional Business Image`,\n      credit: 'Unsplash Source API',\n      source: 'unsplash_direct',\n      quality_score: 9,\n      width: 1200,\n      height: 630\n    },\n    {\n      id: 'picsum_1',\n      url: 'https://picsum.photos/1200/630?random=1',\n      url_hd: 'https://picsum.photos/1920/1080?random=1',\n      url_thumb: 'https://picsum.photos/400/300?random=1',\n      alt: `${keyword} - High Quality Stock Photo`,\n      credit: 'Lorem Picsum',\n      source: 'picsum',\n      quality_score: 8,\n      width: 1200,\n      height: 630\n    },\n    {\n      id: 'branded_placeholder',\n      url: 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n      url_hd: 'https://via.placeholder.com/1920x1080/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n      url_thumb: 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n      alt: `${keyword} - GOD Digital Marketing`,\n      credit: 'GOD Digital Marketing Brand Asset',\n      source: 'branded_placeholder',\n      quality_score: 7,\n      width: 1200,\n      height: 630\n    }\n  ];\n  \n  selectedImages = fallbackImages;\n  primaryImage = fallbackImages[0]; // Use Unsplash direct as first choice\n}\n\n// Platform-specific image optimization\nconst platformOptimizedImages = {\n  // Facebook optimized images\n  facebook_feed: primaryImage.url.includes('placeholder') \n    ? primaryImage.url \n    : primaryImage.url,\n  facebook_story: primaryImage.url.replace('1200x630', '1080x1920'),\n  facebook_carousel: primaryImage.url.replace('1200x630', '1080x1080'),\n  \n  // Instagram optimized images\n  instagram_feed: primaryImage.url.replace('1200x630', '1080x1080'),\n  instagram_story: primaryImage.url.replace('1200x630', '1080x1920'),\n  instagram_reel: primaryImage.url.replace('1200x630', '1080x1920'),\n  \n  // LinkedIn optimized images\n  linkedin_post: primaryImage.url.replace('1200x630', '1200x627'),\n  linkedin_article: primaryImage.url.replace('1200x630', '1128x376'),\n  \n  // Twitter optimized images\n  twitter_post: primaryImage.url.replace('1200x630', '1200x675'),\n  twitter_header: primaryImage.url.replace('1200x630', '1500x500'),\n  \n  // YouTube optimized images\n  youtube_thumbnail: primaryImage.url.replace('1200x630', '1280x720'),\n  youtube_banner: primaryImage.url.replace('1200x630', '2560x1440'),\n  \n  // TikTok optimized images\n  tiktok_video: primaryImage.url.replace('1200x630', '1080x1920'),\n  \n  // Pinterest optimized images\n  pinterest_pin: primaryImage.url.replace('1200x630', '1000x1500'),\n  \n  // General purpose images\n  blog_header: primaryImage.url,\n  email_header: primaryImage.url.replace('1200x630', '600x300'),\n  website_banner: primaryImage.url.replace('1200x630', '1920x600')\n};\n\n// Image quality assessment\nconst imageQualityMetrics = {\n  resolution_quality: primaryImage.width >= 1200 && primaryImage.height >= 630,\n  source_reliability: ['unsplash', 'pexels', 'unsplash_direct'].includes(primaryImage.source),\n  content_relevance: true,\n  brand_consistency: primaryImage.alt.includes('GOD Digital Marketing'),\n  platform_optimization: Object.keys(platformOptimizedImages).length >= 15\n};\n\nconst imageQualityScore = Object.values(imageQualityMetrics).filter(Boolean).length / Object.keys(imageQualityMetrics).length * 10;\n\n// Advanced image metadata\nconst imageMetadata = {\n  primary_image: primaryImage,\n  alternative_images: selectedImages.slice(1, 3),\n  platform_optimized: platformOptimizedImages,\n  \n  quality_assessment: {\n    ...imageQualityMetrics,\n    overall_score: imageQualityScore,\n    quality_pass: imageQualityScore >= 7,\n    optimization_level: imageQualityScore >= 9 ? 'excellent' : imageQualityScore >= 7 ? 'good' : 'acceptable'\n  },\n  \n  ai_recommendations: {\n    best_platforms: imageQualityScore >= 9 ? \n      ['instagram', 'pinterest', 'linkedin', 'facebook'] : \n      ['facebook', 'linkedin', 'twitter'],\n    content_type_match: strategy.type || 'educational',\n    visual_appeal: imageQualityScore >= 8 ? 'high' : 'medium',\n    engagement_potential: imageQualityScore >= 9 ? 'viral' : imageQualityScore >= 7 ? 'high' : 'medium'\n  },\n  \n  technical_specs: {\n    original_dimensions: `${primaryImage.width}x${primaryImage.height}`,\n    aspect_ratio: primaryImage.width && primaryImage.height ? \n      (primaryImage.width / primaryImage.height).toFixed(2) : '1.9',\n    file_format: 'JPEG/WebP optimized',\n    compression: 'Optimized for web delivery'\n  },\n  \n  api_status: {\n    unsplash_connected: Object.keys(unsplashData).length > 0,\n    unsplash_results: unsplashData.results ? unsplashData.results.length : 0,\n    pexels_connected: Object.keys(pexelsData).length > 0,\n    pexels_results: pexelsData.photos ? pexelsData.photos.length : 0,\n    fallback_used: primaryImage.source.includes('direct') || primaryImage.source.includes('picsum') || primaryImage.source.includes('placeholder')\n  }\n};\n\n// Content-image alignment score\nconst contentImageAlignment = {\n  keyword_match: true,\n  strategy_alignment: true,\n  brand_consistency: primaryImage.alt.includes('GOD Digital Marketing'),\n  quality_threshold: imageQualityScore >= 7\n};\n\nconst alignmentScore = Object.values(contentImageAlignment).filter(Boolean).length / Object.keys(contentImageAlignment).length * 10;\n\nreturn {\n  ...imageMetadata,\n  content_alignment: {\n    ...contentImageAlignment,\n    alignment_score: alignmentScore,\n    alignment_pass: alignmentScore >= 7\n  },\n  processing_complete: true,\n  ai_optimized: true,\n  ready_for_posting: imageQualityScore >= 7 && alignmentScore >= 7,\n  timestamp: new Date().toISOString()\n};"}, "id": "b0e40034-14d8-40fc-a0c0-9a4ad7c98783", "name": "Ultimate AI Image Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-360, 280]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/me/feed", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": "={{ $('Ultimate AI Content Processor').item.json.facebook_post }}"}, {"name": "link", "value": "https://godigitalmarketing.com"}, {"name": "published", "value": true}]}, "options": {"timeout": 30000}}, "id": "ae2d8034-cfb4-4c4e-ad16-226520562b48", "name": "Facebook Advanced Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-160, -240], "credentials": {"facebookGraphApi": {"id": "O3Lat9arrTvetH1k", "name": "Facebook Graph account"}}}, {"parameters": {"url": "https://graph.facebook.com/v18.0/me/photos", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $('Ultimate AI Image Processor').item.json.platform_optimized.instagram_feed }}"}, {"name": "caption", "value": "={{ $('Ultimate AI Content Processor').item.json.instagram_caption }}"}, {"name": "published", "value": true}]}, "options": {"timeout": 30000}}, "id": "8ad5c400-7919-48fb-a391-b30decf86782", "name": "Instagram Advanced Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-160, -100], "credentials": {"facebookGraphApi": {"id": "O3Lat9arrTvetH1k", "name": "Facebook Graph account"}}}, {"parameters": {"jsCode": "// Ultimate AI-Powered Performance Analytics & Intelligence System\nconst platforms = [\n  'Facebook Advanced Post',\n  'Instagram Advanced Post',\n  'Twitter Advanced Post',\n  'Twitter Advanced Thread',\n  'LinkedIn Advanced Post',\n  'YouTube Advanced Upload',\n  'Pinterest Advanced Pin',\n  'Reddit Advanced Post',\n  'TikTok Advanced Post'\n];\n\nconst results = [];\nconst errors = [];\nconst warnings = [];\n\n// Advanced Result Collection with Error Analysis\nplatforms.forEach(platform => {\n  try {\n    const result = $(platform).item;\n    if (result && result.json) {\n      const statusCode = result.json.statusCode || 200;\n      const success = statusCode >= 200 && statusCode < 300;\n      \n      results.push({\n        platform: platform.replace(' Advanced', '').replace(' Post', '').replace(' Thread', '').replace(' Upload', '').replace(' Pin', ''),\n        status: success ? 'success' : 'warning',\n        response: result.json,\n        status_code: statusCode,\n        timestamp: new Date().toISOString(),\n        performance_score: success ? 10 : 6\n      });\n      \n      if (!success) {\n        warnings.push({\n          platform: platform,\n          issue: `HTTP ${statusCode}`,\n          severity: 'medium'\n        });\n      }\n    } else {\n      errors.push({\n        platform: platform,\n        status: 'failed',\n        error: 'No response data',\n        timestamp: new Date().toISOString(),\n        severity: 'high'\n      });\n    }\n  } catch (error) {\n    errors.push({\n      platform: platform,\n      status: 'failed',\n      error: error.message,\n      timestamp: new Date().toISOString(),\n      severity: 'high'\n    });\n  }\n});\n\n// Advanced Performance Calculations\nconst successCount = results.filter(r => r.status === 'success').length;\nconst warningCount = results.filter(r => r.status === 'warning').length;\nconst failureCount = errors.length;\nconst totalPlatforms = platforms.length;\nconst successRate = (successCount / totalPlatforms) * 100;\nconst reliabilityScore = ((successCount + (warningCount * 0.5)) / totalPlatforms) * 100;\n\n// Get content and configuration data\nconst contentData = $('Ultimate AI Content Processor').item.json;\nconst imageData = $('Ultimate AI Image Processor').item.json;\nconst config = $('Ultimate AI Configuration').item.json;\nconst audienceIntel = $('AI Audience Intelligence').item.json;\nconst trendData = $('Advanced AI Trend Analyzer').item.json;\n\n// AI Performance Prediction Model\nconst performancePrediction = {\n  engagement_forecast: {\n    expected_likes: Math.round(audienceIntel.content_recommendations.expected_engagement * 1000),\n    expected_shares: Math.round(audienceIntel.content_recommendations.expected_engagement * 200),\n    expected_comments: Math.round(audienceIntel.content_recommendations.expected_engagement * 150),\n    expected_clicks: Math.round(audienceIntel.content_recommendations.expected_engagement * 300)\n  },\n  viral_potential: contentData.quality_metrics.overall_score >= 9 ? 'high' : \n                  contentData.quality_metrics.overall_score >= 7 ? 'medium' : 'low',\n  conversion_likelihood: contentData.quality_metrics.quality_pass ? 'high' : 'medium',\n  roi_projection: {\n    estimated_reach: successCount * 5000,\n    estimated_leads: Math.round(successCount * 25),\n    estimated_revenue: Math.round(successCount * 25 * 500), // $500 average customer value\n    cost_per_lead: 0, // Organic social media\n    roi_percentage: 'Infinite (organic content)'\n  }\n};\n\n// Advanced Quality Assessment\nconst campaignQuality = {\n  content_quality: contentData.quality_metrics.overall_score,\n  image_quality: imageData.quality_assessment.overall_score,\n  platform_optimization: reliabilityScore,\n  trend_integration: trendData.selected_trends.length > 0 ? 10 : 5,\n  audience_alignment: audienceIntel.optimization_score,\n  overall_campaign_score: (\n    contentData.quality_metrics.overall_score +\n    imageData.quality_assessment.overall_score +\n    (reliabilityScore / 10) +\n    audienceIntel.optimization_score\n  ) / 4\n};\n\n// Intelligent Recommendations\nconst aiRecommendations = {\n  immediate_actions: [],\n  optimization_suggestions: [],\n  next_campaign_improvements: []\n};\n\nif (successRate < 80) {\n  aiRecommendations.immediate_actions.push('Review failed platform credentials');\n  aiRecommendations.immediate_actions.push('Implement retry mechanism for failed posts');\n}\n\nif (contentData.quality_metrics.overall_score < 8) {\n  aiRecommendations.optimization_suggestions.push('Enhance content psychological triggers');\n  aiRecommendations.optimization_suggestions.push('Improve call-to-action strength');\n}\n\nif (imageData.quality_assessment.overall_score < 8) {\n  aiRecommendations.optimization_suggestions.push('Use higher quality images');\n  aiRecommendations.optimization_suggestions.push('Improve image-content alignment');\n}\n\naiRecommendations.next_campaign_improvements.push('Implement A/B testing for content variations');\naiRecommendations.next_campaign_improvements.push('Add video content for higher engagement');\naiRecommendations.next_campaign_improvements.push('Integrate user-generated content');\n\n// Campaign Summary\nconst campaignSummary = {\n  campaign_id: `GOD_${config.rotation_day}_${new Date().toISOString().split('T')[0]}`,\n  content_type: contentData.content_type,\n  content_focus: contentData.content_focus,\n  day_strategy: config.day_name,\n  psychological_triggers: contentData.psychological_triggers,\n  trending_topics: contentData.trending_topics,\n  primary_keywords: contentData.primary_keywords\n};\n\nreturn {\n  // Performance Metrics\n  posting_results: results,\n  posting_errors: errors,\n  posting_warnings: warnings,\n  success_count: successCount,\n  warning_count: warningCount,\n  failure_count: failureCount,\n  total_platforms: totalPlatforms,\n  success_rate: successRate,\n  reliability_score: reliabilityScore,\n  \n  // Quality Assessment\n  campaign_quality: campaignQuality,\n  \n  // AI Predictions\n  performance_prediction: performancePrediction,\n  \n  // Campaign Data\n  campaign_summary: campaignSummary,\n  \n  // AI Recommendations\n  ai_recommendations: aiRecommendations,\n  \n  // Status Flags\n  needs_retry: failureCount > 0,\n  campaign_success: successRate >= 70,\n  quality_threshold_met: campaignQuality.overall_campaign_score >= 8,\n  \n  // Metadata\n  analysis_complete: true,\n  ai_enhanced: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "e34552e2-435c-4bc5-8f5b-706c0ecd0227", "name": "Ultimate AI Analytics Engine", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 160]}, {"parameters": {"text": "🚀 *GOD Digital Marketing - ULTIMATE Campaign Report*\\n\\n*📅 {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.day_strategy }} - {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.content_type.toUpperCase() }} Strategy*\\n\\n*🎯 CAMPAIGN PERFORMANCE:*\\n• Success Rate: {{ $('Ultimate AI Analytics Engine').item.json.success_rate }}%\\n• Reliability Score: {{ $('Ultimate AI Analytics Engine').item.json.reliability_score }}%\\n• Platforms Deployed: {{ $('Ultimate AI Analytics Engine').item.json.success_count }}/{{ $('Ultimate AI Analytics Engine').item.json.total_platforms }}\\n• Campaign Quality: {{ $('Ultimate AI Analytics Engine').item.json.campaign_quality.overall_campaign_score }}/10\\n\\n*🧠 AI INTELLIGENCE METRICS:*\\n• Content Quality: {{ $('Ultimate AI Analytics Engine').item.json.campaign_quality.content_quality }}/10\\n• Image Quality: {{ $('Ultimate AI Analytics Engine').item.json.campaign_quality.image_quality }}/10\\n• Audience Alignment: {{ $('Ultimate AI Analytics Engine').item.json.campaign_quality.audience_alignment }}/10\\n• Trend Integration: {{ $('Ultimate AI Analytics Engine').item.json.campaign_quality.trend_integration }}/10\\n\\n*📊 AI PERFORMANCE PREDICTIONS:*\\n• Expected Engagement: {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.engagement_forecast.expected_likes }} likes, {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.engagement_forecast.expected_shares }} shares\\n• Viral Potential: {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.viral_potential.toUpperCase() }}\\n• Conversion Likelihood: {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.conversion_likelihood.toUpperCase() }}\\n• Estimated Reach: {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.roi_projection.estimated_reach }} people\\n• Projected Leads: {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.roi_projection.estimated_leads }}\\n• Revenue Potential: ${{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.roi_projection.estimated_revenue }}\\n\\n*🎨 TODAY'S STRATEGY:*\\n• Focus: {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.content_focus }}\\n• Psychology: {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.psychological_triggers }}\\n• Keywords: {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.primary_keywords.join(', ') }}\\n• Trends: {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.trending_topics.join(', ') }}\\n\\n*📱 ADVANCED PLATFORM DEPLOYMENT:*\\n• ✅ Facebook (Advanced Feed + Stories)\\n• ✅ Instagram (Feed + Stories + Reels)\\n• ✅ Twitter/X (Advanced Threads + Posts)\\n• ✅ LinkedIn (Professional + Articles)\\n• ✅ YouTube (Videos + Descriptions)\\n• ✅ Pinterest (SEO-Optimized Pins)\\n• ✅ Reddit (Community Engagement)\\n• ✅ TikTok (Viral Video Content)\\n\\n*🤖 AI RECOMMENDATIONS:*\\n{{ $('Ultimate AI Analytics Engine').item.json.ai_recommendations.optimization_suggestions.length > 0 ? '• Optimizations: ' + $('Ultimate AI Analytics Engine').item.json.ai_recommendations.optimization_suggestions.join(', ') : '• All systems optimized!' }}\\n{{ $('Ultimate AI Analytics Engine').item.json.ai_recommendations.immediate_actions.length > 0 ? '• Actions: ' + $('Ultimate AI Analytics Engine').item.json.ai_recommendations.immediate_actions.join(', ') : '• No immediate actions required' }}\\n\\n*💰 ROI IMPACT:*\\n• Investment: $0 (100% Automated)\\n• Time Saved: 8+ hours of manual work\\n• Reach Potential: {{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.roi_projection.estimated_reach }}+ people\\n• Lead Generation: High-converting, AI-optimized content\\n• Revenue Projection: ${{ $('Ultimate AI Analytics Engine').item.json.performance_prediction.roi_projection.estimated_revenue }}+\\n\\n*🔮 NEXT CAMPAIGN:*\\n• Tomorrow: {{ ['Educational Excellence', 'Achievement Showcase', 'Marketing Psychology', 'Industry Trends', 'Value-Driven Resources', 'Community Engagement', 'Motivational Content'][($('Ultimate AI Analytics Engine').item.json.campaign_summary.day_strategy === 'Sunday' ? 1 : (['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].indexOf($('Ultimate AI Analytics Engine').item.json.campaign_summary.day_strategy) + 1))] }}\\n• AI Optimization: Continuous learning and improvement\\n• Performance Tracking: Real-time analytics and adjustments\\n\\n*Campaign ID:* {{ $('Ultimate AI Analytics Engine').item.json.campaign_summary.campaign_id }}\\n\\n*🎉 GOD Digital Marketing - Dominating Social Media with AI Intelligence!*", "otherOptions": {}}, "id": "7d2484bd-95da-447f-ad2d-2a9a26dc7b3a", "name": "Ultimate Success Report", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [460, 120], "webhookId": "YOUR_SLACK_WEBHOOK_ID", "disabled": true}, {"parameters": {"text": "={{ $('Ultimate AI Content Processor').item.json.linkedin_post }}", "additionalFields": {"visibility": "public"}}, "id": "a8699cea-ec99-4b1e-ae9d-ead236a1f7ed", "name": "LinkedIn Advanced Post", "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [-140, 120], "credentials": {"linkedInOAuth2Api": {"id": "Gg3X9KirNLs7kfyM", "name": "LinkedIn account"}}}, {"parameters": {"url": "https://www.reddit.com/api/submit", "authentication": "predefinedCredentialType", "nodeCredentialType": "redditOAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "sr", "value": "entrepreneur"}, {"name": "kind", "value": "self"}, {"name": "title", "value": "={{ $('Ultimate AI Content Processor').item.json.reddit_title }}"}, {"name": "text", "value": "={{ $('Ultimate AI Content Processor').item.json.reddit_post }}"}, {"name": "sendreplies", "value": true}]}, "options": {"timeout": 30000}}, "id": "0860a9e5-2388-493f-a356-bbd9578a605e", "name": "Reddit Advanced Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-100, 620]}, {"parameters": {"url": "https://api.pinterest.com/v5/pins", "authentication": "predefinedCredentialType", "nodeCredentialType": "pinterestOAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "board_id", "value": "YOUR_PINTEREST_BOARD_ID"}, {"name": "media_source", "value": {"source_type": "image_url", "url": "={{ $('Ultimate AI Image Processor').item.json.platform_optimized.pinterest_pin }}"}}, {"name": "title", "value": "={{ $('Ultimate AI Content Processor').item.json.pinterest_title }}"}, {"name": "description", "value": "={{ $('Ultimate AI Content Processor').item.json.pinterest_description }}"}, {"name": "link", "value": "https://godigitalmarketing.com"}, {"name": "alt_text", "value": "={{ $('Ultimate AI Image Processor').item.json.primary_image.alt }}"}]}, "options": {"timeout": 30000}}, "id": "5b3c7243-d85b-4275-b0d0-202b0e191b61", "name": "Pinterest Advanced Pin", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-120, 440]}, {"parameters": {"url": "https://www.googleapis.com/youtube/v3/videos", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet,status"}, {"name": "uploadType", "value": "media"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "snippet", "value": {"title": "={{ $('Ultimate AI Content Processor').item.json.youtube_title }}", "description": "={{ $('Ultimate AI Content Processor').item.json.youtube_description }}", "tags": ["digital marketing", "AI automation", "business growth", "GOD Digital Marketing"], "categoryId": "22", "defaultLanguage": "en"}}, {"name": "status", "value": {"privacyStatus": "public", "publishAt": "={{ new Date(Date.now() + 3600000).toISOString() }}"}}]}, "options": {"timeout": 60000}}, "id": "cf8e381f-c85a-4294-becb-73950e415510", "name": "YouTube Advanced Upload", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-120, 260]}, {"parameters": {"text": "={{ $('Ultimate AI Content Processor').item.json.twitter_single }}", "additionalFields": {}}, "id": "twitter-post-node-001", "name": "Twitter Advanced Post", "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [-160, 40], "credentials": {"twitterOAuth2Api": {"id": "twitter-credentials-001", "name": "Twitter account"}}}, {"parameters": {"text": "={{ $('Ultimate AI Content Processor').item.json.twitter_thread }}", "additionalFields": {"attachments": "={{ $('Ultimate AI Image Processor').item.json.platform_optimized.twitter_card }}"}}, "id": "twitter-thread-node-001", "name": "Twitter Advanced Thread", "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [-160, -20], "credentials": {"twitterOAuth2Api": {"id": "twitter-credentials-001", "name": "Twitter account"}}}, {"parameters": {"url": "https://open-api.tiktok.com/share/video/upload/", "authentication": "predefinedCredentialType", "nodeCredentialType": "tiktokApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "video_url", "value": "={{ $('Ultimate AI Image Processor').item.json.platform_optimized.tiktok_video || $('Ultimate AI Image Processor').item.json.primary_image.url }}"}, {"name": "text", "value": "={{ $('Ultimate AI Content Processor').item.json.tiktok_script }}"}, {"name": "privacy_level", "value": "PUBLIC_TO_EVERYONE"}]}, "options": {"timeout": 30000}}, "id": "tiktok-post-node-001", "name": "TikTok Advanced Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-160, 80], "credentials": {"tiktokApi": {"id": "tiktok-credentials-001", "name": "TikTok account"}}}], "connections": {"Intelligent AI Scheduler": {"main": [[{"node": "Ultimate AI Configuration", "type": "main", "index": 0}]]}, "Manual Test Trigger": {"main": [[{"node": "Ultimate AI Configuration", "type": "main", "index": 0}]]}, "Ultimate AI Configuration": {"main": [[{"node": "AI Audience Intelligence", "type": "main", "index": 0}]]}, "AI Audience Intelligence": {"main": [[{"node": "Multi-Source Trend Research", "type": "main", "index": 0}, {"node": "Industry News Research", "type": "main", "index": 0}]]}, "Multi-Source Trend Research": {"main": [[{"node": "Advanced AI Trend Analyzer", "type": "main", "index": 0}]]}, "Industry News Research": {"main": [[{"node": "Advanced AI Trend Analyzer", "type": "main", "index": 0}]]}, "Advanced AI Trend Analyzer": {"main": [[{"node": "Ultimate Content Creator AI", "type": "main", "index": 0}]]}, "Primary AI Model (Llama 3.1)": {"ai_languageModel": [[{"node": "Ultimate Content Creator AI", "type": "ai_languageModel", "index": 0}]]}, "Ultimate Content Creator AI": {"main": [[{"node": "Ultimate AI Content Processor", "type": "main", "index": 0}]]}, "Ultimate AI Content Processor": {"main": [[{"node": "Primary Image Search (Unsplash)", "type": "main", "index": 0}, {"node": "Backup Image Search (Pexels)", "type": "main", "index": 0}]]}, "Primary Image Search (Unsplash)": {"main": [[{"node": "Ultimate AI Image Processor", "type": "main", "index": 0}]]}, "Backup Image Search (Pexels)": {"main": [[{"node": "Ultimate AI Image Processor", "type": "main", "index": 0}]]}, "Ultimate AI Image Processor": {"main": [[{"node": "Facebook Advanced Post", "type": "main", "index": 0}, {"node": "Instagram Advanced Post", "type": "main", "index": 0}, {"node": "Twitter Advanced Post", "type": "main", "index": 0}, {"node": "Twitter Advanced Thread", "type": "main", "index": 0}, {"node": "LinkedIn Advanced Post", "type": "main", "index": 0}, {"node": "Reddit Advanced Post", "type": "main", "index": 0}, {"node": "Pinterest Advanced Pin", "type": "main", "index": 0}, {"node": "YouTube Advanced Upload", "type": "main", "index": 0}, {"node": "TikTok Advanced Post", "type": "main", "index": 0}]]}, "Facebook Advanced Post": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "Instagram Advanced Post": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "Ultimate AI Analytics Engine": {"main": [[{"node": "Ultimate Success Report", "type": "main", "index": 0}]]}, "Twitter Advanced Post": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "Twitter Advanced Thread": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "LinkedIn Advanced Post": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "Reddit Advanced Post": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "Pinterest Advanced Pin": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "YouTube Advanced Upload": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}, "TikTok Advanced Post": {"main": [[{"node": "Ultimate AI Analytics Engine", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "fa71618849152fdf81b026b7e79a6c24770db503a9228ddbbcab15c2a292ea40"}}