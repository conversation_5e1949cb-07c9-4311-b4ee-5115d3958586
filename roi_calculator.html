<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GOD Digital Marketing - ROI Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1E40AF 0%, #10B981 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .calculator {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
        }
        
        .input-section {
            background: #f8fafc;
            padding: 30px;
            border-radius: 15px;
        }
        
        .results-section {
            background: #1E40AF;
            color: white;
            padding: 30px;
            border-radius: 15px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #1E40AF;
        }
        
        .result-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .result-card h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .result-value {
            font-size: 2rem;
            font-weight: bold;
            color: #10B981;
        }
        
        .calculate-btn {
            width: 100%;
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .calculate-btn:hover {
            transform: translateY(-2px);
        }
        
        .benefits {
            grid-column: 1 / -1;
            background: #f0fdf4;
            padding: 30px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .benefits h3 {
            color: #166534;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .benefit-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #10B981;
        }
        
        .benefit-item h4 {
            color: #166534;
            margin-bottom: 10px;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .cta-btn {
            background: white;
            color: #D97706;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
            transition: transform 0.2s;
        }
        
        .cta-btn:hover {
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .calculator {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Social Media Automation ROI Calculator</h1>
            <p>Calculate your potential return on investment with GOD Digital Marketing's automation system</p>
        </div>
        
        <div class="calculator">
            <div class="input-section">
                <h2 style="margin-bottom: 30px; color: #1E40AF;">Your Current Situation</h2>
                
                <div class="form-group">
                    <label for="currentHours">Hours spent on social media per week:</label>
                    <input type="number" id="currentHours" value="20" min="1" max="80">
                </div>
                
                <div class="form-group">
                    <label for="hourlyRate">Your hourly rate ($):</label>
                    <input type="number" id="hourlyRate" value="50" min="10" max="500">
                </div>
                
                <div class="form-group">
                    <label for="currentLeads">Current monthly leads from social media:</label>
                    <input type="number" id="currentLeads" value="10" min="0" max="1000">
                </div>
                
                <div class="form-group">
                    <label for="leadValue">Average value per lead ($):</label>
                    <input type="number" id="leadValue" value="500" min="50" max="10000">
                </div>
                
                <div class="form-group">
                    <label for="conversionRate">Lead to customer conversion rate (%):</label>
                    <input type="number" id="conversionRate" value="20" min="1" max="100">
                </div>
                
                <button class="calculate-btn" onclick="calculateROI()">Calculate My ROI</button>
            </div>
            
            <div class="results-section">
                <h2 style="margin-bottom: 30px;">Your Potential Results</h2>
                
                <div class="result-card">
                    <h3>Monthly Time Savings</h3>
                    <div class="result-value" id="timeSavings">160 hours</div>
                </div>
                
                <div class="result-card">
                    <h3>Monthly Cost Savings</h3>
                    <div class="result-value" id="costSavings">$8,000</div>
                </div>
                
                <div class="result-card">
                    <h3>Additional Monthly Leads</h3>
                    <div class="result-value" id="additionalLeads">140 leads</div>
                </div>
                
                <div class="result-card">
                    <h3>Additional Monthly Revenue</h3>
                    <div class="result-value" id="additionalRevenue">$14,000</div>
                </div>
                
                <div class="result-card">
                    <h3>Monthly ROI</h3>
                    <div class="result-value" id="monthlyROI">36,567%</div>
                </div>
                
                <div class="result-card">
                    <h3>Annual ROI</h3>
                    <div class="result-value" id="annualROI">3,047%</div>
                </div>
            </div>
            
            <div class="benefits">
                <h3>🎯 What You Get With Our Automation System</h3>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <h4>⏰ Time Freedom</h4>
                        <p>Reduce social media management from 20+ hours to 2 hours per week</p>
                    </div>
                    <div class="benefit-item">
                        <h4>📈 More Leads</h4>
                        <p>Generate 10x more qualified leads through AI-optimized content</p>
                    </div>
                    <div class="benefit-item">
                        <h4>💰 Cost Effective</h4>
                        <p>Complete system costs less than $75/month - infinite ROI</p>
                    </div>
                    <div class="benefit-item">
                        <h4>🤖 AI-Powered</h4>
                        <p>Advanced AI creates platform-optimized content automatically</p>
                    </div>
                    <div class="benefit-item">
                        <h4>📊 Analytics</h4>
                        <p>Comprehensive tracking and optimization recommendations</p>
                    </div>
                    <div class="benefit-item">
                        <h4>🚀 Scalable</h4>
                        <p>Grows with your business - from startup to enterprise</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="cta-section">
            <h2>Ready to Transform Your Social Media Marketing?</h2>
            <p>Join 500+ businesses already using our automation system to generate massive ROI</p>
            <button class="cta-btn" onclick="window.open('https://godigitalmarketing.com', '_blank')">
                Get Started Today - Free Strategy Call
            </button>
        </div>
    </div>
    
    <script>
        function calculateROI() {
            // Get input values
            const currentHours = parseFloat(document.getElementById('currentHours').value) || 20;
            const hourlyRate = parseFloat(document.getElementById('hourlyRate').value) || 50;
            const currentLeads = parseFloat(document.getElementById('currentLeads').value) || 10;
            const leadValue = parseFloat(document.getElementById('leadValue').value) || 500;
            const conversionRate = parseFloat(document.getElementById('conversionRate').value) || 20;
            
            // Calculate current monthly costs
            const currentMonthlyCost = currentHours * 4 * hourlyRate; // 4 weeks per month
            
            // Calculate automation benefits
            const automationHours = 2; // Hours per week with automation
            const newMonthlyCost = automationHours * 4 * hourlyRate;
            const systemCost = 75; // Monthly system cost
            
            // Time and cost savings
            const timeSavings = (currentHours - automationHours) * 4;
            const costSavings = currentMonthlyCost - newMonthlyCost - systemCost;
            
            // Lead generation improvements (conservative 10x increase)
            const newLeads = currentLeads * 10;
            const additionalLeads = newLeads - currentLeads;
            
            // Revenue calculations
            const currentRevenue = currentLeads * (conversionRate / 100) * leadValue;
            const newRevenue = newLeads * (conversionRate / 100) * leadValue;
            const additionalRevenue = newRevenue - currentRevenue;
            
            // ROI calculations
            const totalMonthlySavings = costSavings + additionalRevenue;
            const monthlyInvestment = systemCost;
            const monthlyROI = ((totalMonthlySavings - monthlyInvestment) / monthlyInvestment) * 100;
            const annualROI = ((totalMonthlySavings * 12 - monthlyInvestment * 12) / (monthlyInvestment * 12)) * 100;
            
            // Update display
            document.getElementById('timeSavings').textContent = Math.round(timeSavings) + ' hours';
            document.getElementById('costSavings').textContent = '$' + Math.round(costSavings).toLocaleString();
            document.getElementById('additionalLeads').textContent = Math.round(additionalLeads) + ' leads';
            document.getElementById('additionalRevenue').textContent = '$' + Math.round(additionalRevenue).toLocaleString();
            document.getElementById('monthlyROI').textContent = Math.round(monthlyROI).toLocaleString() + '%';
            document.getElementById('annualROI').textContent = Math.round(annualROI).toLocaleString() + '%';
        }
        
        // Calculate on page load
        calculateROI();
        
        // Recalculate when inputs change
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', calculateROI);
        });
    </script>
</body>
</html>
