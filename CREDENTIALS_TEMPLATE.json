{"credentials_template": {"groq_api": {"id": "7mnRPxIiDsoggo51", "name": "Groq account", "type": "groqApi", "data": {"apiKey": "YOUR_GROQ_API_KEY_HERE"}, "setup_url": "https://console.groq.com", "required_permissions": ["API Access"], "notes": "Free tier available with generous limits"}, "facebook_graph_api": {"id": "O3Lat9arrTvetH1k", "name": "Facebook Graph account", "type": "facebookGraphApi", "data": {"accessToken": "YOUR_FACEBOOK_PAGE_ACCESS_TOKEN"}, "setup_url": "https://developers.facebook.com", "required_permissions": ["pages_manage_posts", "pages_read_engagement", "instagram_basic", "instagram_content_publish"], "notes": "Requires Facebook Business account and approved app"}, "twitter_oauth2": {"id": "twitter-credentials-001", "name": "Twitter account", "type": "twitterOAuth2Api", "data": {"clientId": "YOUR_TWITTER_CLIENT_ID", "clientSecret": "YOUR_TWITTER_CLIENT_SECRET", "accessToken": "YOUR_TWITTER_ACCESS_TOKEN", "accessTokenSecret": "YOUR_TWITTER_ACCESS_TOKEN_SECRET"}, "setup_url": "https://developer.twitter.com", "required_permissions": ["tweet.write", "users.read"], "notes": "Requires Twitter Developer Account approval"}, "linkedin_oauth2": {"id": "Gg3X9KirNLs7kfyM", "name": "LinkedIn account", "type": "linkedInOAuth2Api", "data": {"clientId": "YOUR_LINKEDIN_CLIENT_ID", "clientSecret": "YOUR_LINKEDIN_CLIENT_SECRET"}, "setup_url": "https://www.linkedin.com/developers", "required_permissions": ["w_member_social"], "notes": "Requires LinkedIn Developer Program access"}, "reddit_oauth2": {"id": "reddit-credentials-001", "name": "Reddit account", "type": "redditOAuth2Api", "data": {"clientId": "YOUR_REDDIT_CLIENT_ID", "clientSecret": "YOUR_REDDIT_CLIENT_SECRET", "username": "YOUR_REDDIT_USERNAME", "password": "YOUR_REDDIT_PASSWORD"}, "setup_url": "https://www.reddit.com/prefs/apps", "required_permissions": ["submit", "read"], "notes": "Create script app type for posting"}, "pinterest_oauth2": {"id": "pinterest-credentials-001", "name": "Pinterest account", "type": "pinterestOAuth2Api", "data": {"clientId": "YOUR_PINTEREST_CLIENT_ID", "clientSecret": "YOUR_PINTEREST_CLIENT_SECRET"}, "setup_url": "https://developers.pinterest.com", "required_permissions": ["boards:write", "pins:write"], "notes": "Requires Pinterest Business account"}, "youtube_oauth2": {"id": "youtube-credentials-001", "name": "YouTube account", "type": "youTubeOAuth2Api", "data": {"clientId": "YOUR_GOOGLE_CLIENT_ID", "clientSecret": "YOUR_GOOGLE_CLIENT_SECRET"}, "setup_url": "https://console.cloud.google.com", "required_permissions": ["youtube.upload"], "notes": "Enable YouTube Data API v3 in Google Cloud Console"}, "tiktok_api": {"id": "tiktok-credentials-001", "name": "TikTok account", "type": "tiktokApi", "data": {"clientKey": "YOUR_TIKTOK_CLIENT_KEY", "clientSecret": "YOUR_TIKTOK_CLIENT_SECRET"}, "setup_url": "https://developers.tiktok.com", "required_permissions": ["video.upload"], "notes": "Requires TikTok for Business API approval"}, "slack_webhook": {"id": "slack-webhook-001", "name": "Slack Webhook", "type": "slackWebhook", "data": {"webhookUrl": "YOUR_SLACK_WEBHOOK_URL"}, "setup_url": "https://api.slack.com/messaging/webhooks", "required_permissions": ["incoming-webhook"], "notes": "Optional: For campaign reports and notifications"}}, "api_keys_included": {"unsplash": {"access_key": "*******************************************", "status": "active", "rate_limit": "50 requests/hour", "notes": "Pre-configured for immediate use"}, "pexels": {"access_key": "563492ad6f91700001000001cdf2b1c2c8a04e3bb4f1f9b3d6c8e5c4", "status": "active", "rate_limit": "200 requests/hour", "notes": "Pre-configured for immediate use"}}, "configuration_variables": {"pinterest_board_id": {"current_value": "YOUR_PINTEREST_BOARD_ID", "location": "Pinterest Advanced Pin node", "required": true, "how_to_find": "Go to Pinterest > Your Boards > Copy Board ID from URL"}, "slack_webhook_id": {"current_value": "YOUR_SLACK_WEBHOOK_ID", "location": "Ultimate Success Report node", "required": false, "how_to_find": "Create Slack App > Incoming Webhooks > Copy Webhook URL"}}, "setup_priority": [{"step": 1, "credential": "groq_api", "importance": "critical", "reason": "Required for AI content generation"}, {"step": 2, "credential": "facebook_graph_api", "importance": "high", "reason": "Covers Facebook and Instagram posting"}, {"step": 3, "credential": "twitter_oauth2", "importance": "high", "reason": "Major social media platform"}, {"step": 4, "credential": "linkedin_oauth2", "importance": "high", "reason": "Professional networking and B2B content"}, {"step": 5, "credential": "reddit_oauth2", "importance": "medium", "reason": "Community engagement and trend research"}, {"step": 6, "credential": "pinterest_oauth2", "importance": "medium", "reason": "Visual content and SEO benefits"}, {"step": 7, "credential": "youtube_oauth2", "importance": "medium", "reason": "Video content optimization"}, {"step": 8, "credential": "tiktok_api", "importance": "low", "reason": "Emerging platform with high engagement"}], "testing_checklist": ["✅ Groq AI responds with content generation", "✅ Facebook posts successfully publish", "✅ Instagram posts with images", "✅ Twitter posts and threads work", "✅ LinkedIn professional content posts", "✅ Reddit community posts submit", "✅ Pinterest pins create successfully", "✅ YouTube metadata updates", "✅ TikTok content uploads", "✅ Image APIs return quality images", "✅ Analytics engine processes all platforms", "✅ Slack reports generate (if configured)"]}